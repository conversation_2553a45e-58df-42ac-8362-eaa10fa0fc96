package com.ttv.demo

import android.content.Context
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin
import com.huobi.home.ui.StickyBottomTabPluginFactory

/**
 * 简化的吸底Tab使用示例
 * 展示不同的使用方式
 */
object SimpleStickyTabExample {

    /**
     * 方式1：使用工厂方法（推荐）
     * 自动处理CoIndicator的复制和监听器
     */
    fun createWithFactory(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabTitles: List<String>
    ): StickyBottomTabPlugin {
        
        return StickyBottomTabPluginFactory.createForCoIndicator(
            context = context,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabTitles,
            preserveOriginalListeners = true  // 保留原始监听器
        ).apply {
            enable()
        }
    }

    /**
     * 方式2：使用Builder模式 + 自定义策略
     * 更灵活的配置方式
     */
    fun createWithBuilder(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabTitles: List<String>
    ): StickyBottomTabPlugin {
        
        // 创建自定义的CoIndicator复制策略
        val listenerAdapter = CoIndicatorListenerAdapter(coIndicator)
        val copyStrategy = CoIndicatorCopyStrategy(tabTitles, listenerAdapter)
        
        return StickyBottomTabPlugin.Builder(context)
            .setCoordinatorLayout(coordinatorLayout)
            .setAppBarLayout(appBarLayout)
            .setOriginalTabLayout(coIndicator)
            .setViewPager(viewPager)
            .setTabCopyStrategy(copyStrategy)
            .setAnimationDuration(500L)  // 自定义动画时长
            .setScrollToPosition(0.25f)  // 滚动到屏幕1/4位置
            .setBottomMargin(120)        // 自定义底部边距
            .build()
            .apply {
                enable()
            }
    }

    /**
     * 方式3：简化模式（不保留原始监听器）
     * 适用于简单场景
     */
    fun createSimpleMode(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabTitles: List<String>
    ): StickyBottomTabPlugin {
        
        return StickyBottomTabPluginFactory.createForCoIndicator(
            context = context,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabTitles,
            preserveOriginalListeners = false  // 不保留原始监听器
        ).apply {
            enable()
        }
    }

    /**
     * 方式4：自定义Tab复制策略
     * 展示如何扩展到其他Tab组件
     */
    fun createWithCustomStrategy(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        originalTab: ViewGroup,
        viewPager: ViewPager2
    ): StickyBottomTabPlugin {
        
        // 自定义Tab复制策略
        val customStrategy = object : TabCopyStrategy {
            override fun createCopy(originalTab: ViewGroup, context: Context): ViewGroup {
                // 自定义的Tab复制逻辑
                // 这里可以处理任何类型的Tab组件
                return createCustomTabCopy(originalTab, context)
            }
            
            override fun syncState(original: ViewGroup, copy: ViewGroup) {
                // 自定义的状态同步逻辑
                syncCustomTabState(original, copy)
            }
            
            override fun setupClickListener(copy: ViewGroup, onTabClick: (Int) -> Unit) {
                // 自定义的点击监听器设置
                setupCustomClickListener(copy, onTabClick)
            }
        }
        
        return StickyBottomTabPlugin.Builder(context)
            .setCoordinatorLayout(coordinatorLayout)
            .setAppBarLayout(appBarLayout)
            .setOriginalTabLayout(originalTab)
            .setViewPager(viewPager)
            .setTabCopyStrategy(customStrategy)
            .build()
            .apply {
                enable()
            }
    }
    
    // 辅助方法（示例实现）
    private fun createCustomTabCopy(originalTab: ViewGroup, context: Context): ViewGroup {
        // 实现自定义Tab的复制逻辑
        // 这里只是示例，实际需要根据具体的Tab组件实现
        return originalTab // 简化示例
    }
    
    private fun syncCustomTabState(original: ViewGroup, copy: ViewGroup) {
        // 实现自定义Tab的状态同步
    }
    
    private fun setupCustomClickListener(copy: ViewGroup, onTabClick: (Int) -> Unit) {
        // 实现自定义Tab的点击监听器设置
    }
}

/**
 * 使用示例
 */
class UsageExample {
    
    fun setupStickyTab(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2
    ) {
        val tabTitles = listOf("推荐", "关注", "热门", "视频")
        
        // 选择合适的创建方式
        val plugin = SimpleStickyTabExample.createWithFactory(
            context = context,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabTitles
        )
        
        // 插件已自动启用，无需额外操作
        android.util.Log.d("UsageExample", "吸底Tab插件创建完成")
    }
}
