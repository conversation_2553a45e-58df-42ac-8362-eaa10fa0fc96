package com.huobi.home.ui

import android.content.Context
import android.view.ViewGroup
import android.widget.LinearLayout
import com.google.android.material.tabs.TabLayout

/**
 * CoIndicator专用的Tab复制策略
 * 处理CoIndicator的创建、状态同步和事件监听
 */
class CoIndicatorCopyStrategy(
    private val tabTitles: List<String?>,
    private val coIndicatorListenerAdapter: CoIndicatorListenerAdapter? = null
) : TabCopyStrategy {

    override fun createCopy(originalTab: ViewGroup, context: Context): ViewGroup {
        require(originalTab is com.hbg.module.libkt.custom.indicator.CoIndicator) {
            "CoIndicatorCopyStrategy只能处理CoIndicator类型"
        }

        val stickyCoIndicator = com.hbg.module.libkt.custom.indicator.CoIndicator(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            // 复制原始CoIndicator的样式属性
            tabMode = originalTab.tabMode
            tabGravity = originalTab.tabGravity
        }

        // 使用tabTitles创建Tab
        tabTitles.forEach { title ->
            if (title != null) {
                stickyCoIndicator.addTab(title)
            }
        }

        // 同步选中状态
        val selectedPosition = originalTab.selectedTabPosition
        if (selectedPosition >= 0 && selectedPosition < stickyCoIndicator.tabCount) {
            stickyCoIndicator.getTabAt(selectedPosition)?.select()
        }

        android.util.Log.d("CoIndicatorCopyStrategy", "创建CoIndicator副本完成，Tab数量: ${stickyCoIndicator.tabCount}")
        return stickyCoIndicator
    }

    override fun syncState(original: ViewGroup, copy: ViewGroup) {
        if (original is com.hbg.module.libkt.custom.indicator.CoIndicator && 
            copy is com.hbg.module.libkt.custom.indicator.CoIndicator) {
            val selectedPosition = original.selectedTabPosition
            if (selectedPosition >= 0 && selectedPosition < copy.tabCount) {
                copy.onPageSelected(selectedPosition)
            }
        }
    }

    override fun setupClickListener(copy: ViewGroup, onTabClick: (Int) -> Unit) {
        if (copy is com.hbg.module.libkt.custom.indicator.CoIndicator) {
            copy.setOnTabSelectedListener { position ->
                // 先触发原始监听器
                coIndicatorListenerAdapter?.triggerAllListeners(position)
                // 再触发插件的处理逻辑
                onTabClick(position)
            }
        }
    }
}

/**
 * CoIndicator监听器适配器
 * 负责捕获和转发原始CoIndicator的各种监听器事件
 */
class CoIndicatorListenerAdapter(
    private val coIndicator: com.hbg.module.libkt.custom.indicator.CoIndicator
) {

    private var originalTabSelectedListeners: MutableList<TabLayout.OnTabSelectedListener> = mutableListOf()
    private var originalPageSelectListener: Any? = null
    private var indicatorHelperListener: Any? = null

    init {
        captureOriginalListeners()
    }

    /**
     * 捕获原始CoIndicator上的所有监听器
     */
    private fun captureOriginalListeners() {
        try {
            // 捕获TabLayout的OnTabSelectedListener
            captureTabSelectedListeners()

            // 捕获CoIndicator的PageSelectListener
            capturePageSelectListener()

            // 捕获IndicatorHelper设置的监听器
            captureIndicatorHelperListener()

            android.util.Log.d("CoIndicatorListenerAdapter", "已捕获原始CoIndicator监听器")
        } catch (e: Exception) {
            android.util.Log.e("CoIndicatorListenerAdapter", "捕获原始监听器失败", e)
        }
    }

    /**
     * 捕获TabLayout的OnTabSelectedListener
     * CoIndicator不是TabLayout，跳过TabSelectedListener捕获
     */
    private fun captureTabSelectedListeners() {
        android.util.Log.d("CoIndicatorListenerAdapter", "CoIndicator不支持TabSelectedListener，跳过捕获")
    }

    /**
     * 捕获CoIndicator的PageSelectListener
     */
    private fun capturePageSelectListener() {
        try {
            val listenerField = coIndicator.javaClass.getDeclaredField("listener")
            listenerField.isAccessible = true
            originalPageSelectListener = listenerField.get(coIndicator)
            if (originalPageSelectListener != null) {
                android.util.Log.d("CoIndicatorListenerAdapter", "捕获到PageSelectListener")
            }
        } catch (e: Exception) {
            android.util.Log.w("CoIndicatorListenerAdapter", "捕获PageSelectListener失败", e)
        }
    }

    /**
     * 捕获IndicatorHelper设置的监听器
     */
    private fun captureIndicatorHelperListener() {
        try {
            val navigatorField = coIndicator.javaClass.getDeclaredField("navigator")
            navigatorField.isAccessible = true
            indicatorHelperListener = navigatorField.get(coIndicator)
            if (indicatorHelperListener != null) {
                android.util.Log.d("CoIndicatorListenerAdapter", "捕获到IndicatorHelper监听器")
            }
        } catch (e: Exception) {
            android.util.Log.w("CoIndicatorListenerAdapter", "捕获IndicatorHelper监听器失败", e)
        }
    }

    /**
     * 触发所有捕获的监听器
     */
    fun triggerAllListeners(position: Int) {
        android.util.Log.d("CoIndicatorListenerAdapter", "触发所有监听器: position=$position")

        // 触发TabSelectedListener
        triggerTabSelectedListeners(position)

        // 触发PageSelectListener
        triggerPageSelectListener(position)

        // 触发IndicatorHelper监听器
        triggerIndicatorHelperListener(position)
    }

    /**
     * 触发TabSelectedListener
     * CoIndicator不支持TabSelectedListener，跳过触发
     */
    private fun triggerTabSelectedListeners(position: Int) {
        android.util.Log.d("CoIndicatorListenerAdapter", "CoIndicator不支持TabSelectedListener，跳过触发")
    }

    /**
     * 触发PageSelectListener
     */
    private fun triggerPageSelectListener(position: Int) {
        try {
            originalPageSelectListener?.let { listener ->
                val onSelectedMethod = listener.javaClass.getDeclaredMethod("onSelected", Int::class.java)
                onSelectedMethod.invoke(listener, position)
                android.util.Log.d("CoIndicatorListenerAdapter", "已触发PageSelectListener")
            }
        } catch (e: Exception) {
            android.util.Log.e("CoIndicatorListenerAdapter", "触发PageSelectListener失败", e)
        }
    }

    /**
     * 触发IndicatorHelper监听器
     */
    private fun triggerIndicatorHelperListener(position: Int) {
        try {
            indicatorHelperListener?.let { navigator ->
                // 尝试调用CommonNavigator的onTabClick方法
                val onTabClickMethod = navigator.javaClass.getDeclaredMethod("onTabClick", Int::class.java)
                onTabClickMethod.isAccessible = true
                onTabClickMethod.invoke(navigator, position)
                android.util.Log.d("CoIndicatorListenerAdapter", "已触发IndicatorHelper监听器")
            }
        } catch (e: Exception) {
            android.util.Log.e("CoIndicatorListenerAdapter", "触发IndicatorHelper监听器失败", e)
        }
    }

    /**
     * 销毁适配器，释放资源
     */
    fun destroy() {
        originalTabSelectedListeners.clear()
        originalPageSelectListener = null
        indicatorHelperListener = null
    }
}
