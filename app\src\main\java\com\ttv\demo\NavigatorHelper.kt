package com.hbg.module.libkt.custom.indicator

import android.util.SparseArray
import android.util.SparseBooleanArray
import com.hbg.module.libkt.custom.indicator.interfaces.OnNavigatorScrollListener

class NavigatorHelper {
    private val deSelItems = SparseBooleanArray()
    private val leavePercents = SparseArray<Float>()

    private var totalCount = 0
    private var currentIndex = 0
    private var lastIndex = 0
    private var scrollState = 0
    private var lastPosOffsetSum = 0f
    private var skimOver = false

    private var navScrollListener: OnNavigatorScrollListener? = null

    private fun dispatchOnEnter(
        index: Int, enterPercent: Float,
        lToR: Boolean, force: Boolean
    ) {
        if (skimOver || index == currentIndex || scrollState == ScrollState.SCROLL_STATE_DRAGGING || force) {
            navScrollListener?.onEnter(index, totalCount, enterPercent, lToR)
            leavePercents.put(index, 1.0f - enterPercent)
        }
    }

    private fun dispatchOnLeave(
        index: Int, leavePercent: Float,
        lToR: Boolean, force: Boolean
    ) {
        if (skimOver || index == lastIndex || scrollState == ScrollState.SCROLL_STATE_DRAGGING
            || (index == currentIndex - 1 || index == currentIndex + 1)
            && leavePercents.get(index, 0.0f) != 1.0f || force
        ) {
            navScrollListener?.onLeave(index, totalCount, leavePercent, lToR)
            leavePercents.put(index, leavePercent)
        }
    }

    private fun dispatchOnSelected(index: Int) {
        navScrollListener?.onSelected(index, totalCount)
        deSelItems.put(index, false)
    }

    private fun dispatchOnDeselected(index: Int) {
        navScrollListener?.onDeselected(index, totalCount)
        deSelItems.put(index, true)
    }

    fun onPageScrolled(pos: Int, offset: Float) {
        val currPosOffsetSum = pos + offset
        var lToR = false
        if (lastPosOffsetSum <= currPosOffsetSum) {
            lToR = true
        }

        if (scrollState != ScrollState.SCROLL_STATE_IDLE) {
            if (currPosOffsetSum == lastPosOffsetSum) {
                return
            }
            var nextPos = pos + 1
            var normalDispatch = true
            if (offset == 0.0f) {
                if (lToR) {
                    nextPos = pos - 1
                    normalDispatch = false
                }
            }
            for (i in 0 until totalCount) {
                if (i == pos || i == nextPos) {
                    continue
                }
                val leavePercent: Float = leavePercents.get(i, 0.0f)
                if (leavePercent != 1.0f) {
                    dispatchOnLeave(i, 1.0f, lToR, true)
                }
            }
            if (normalDispatch) {
                if (lToR) {
                    dispatchOnLeave(pos, offset, lToR = true, force = false)
                    dispatchOnEnter(nextPos, offset, lToR = true, force = false)
                } else {
                    dispatchOnLeave(nextPos, 1.0f - offset, lToR = false, force = false)
                    dispatchOnEnter(pos, 1.0f - offset, lToR = false, force = false)
                }
            } else {
                dispatchOnLeave(nextPos, 1.0f - offset, lToR = true, force = false)
                dispatchOnEnter(pos, 1.0f - offset, lToR = true, force = false)
            }
        } else {
            for (i in 0 until totalCount) {
                if (i == currentIndex) {
                    continue
                }
                val deSelItem: Boolean = deSelItems.get(i)
                if (!deSelItem) {
                    dispatchOnDeselected(i)
                }
                val leavePercent: Float = leavePercents.get(i, 0.0f)
                if (leavePercent != 1.0f) {
                    dispatchOnLeave(i, 1.0f, lToR = false, force = true)
                }
            }
            dispatchOnEnter(currentIndex, 1.0f, lToR = false, force = true)
            dispatchOnSelected(currentIndex)
        }
        lastPosOffsetSum = currPosOffsetSum
    }

    fun onPageSelected(position: Int) {
        lastIndex = currentIndex
        currentIndex = position
        dispatchOnSelected(currentIndex)
        for (i in 0 until totalCount) {
            if (i == currentIndex) {
                continue
            }
            val deselected: Boolean = deSelItems.get(i)
            if (!deselected) {
                dispatchOnDeselected(i)
            }
        }
    }

    fun onPageScrollStateChanged(state: Int) {
        scrollState = state
    }

    fun setNavigatorScrollListener(scrollListener: OnNavigatorScrollListener) {
        navScrollListener = scrollListener
    }

    fun setSkimOver(sOver: Boolean) {
        skimOver = sOver
    }

    fun getTotalCount(): Int {
        return totalCount
    }

    fun setTotalCount(count: Int) {
        totalCount = count
        deSelItems.clear()
        leavePercents.clear()
    }

    fun getCurrentIndex(): Int {
        return currentIndex
    }

    fun getScrollState(): Int {
        return scrollState
    }
}