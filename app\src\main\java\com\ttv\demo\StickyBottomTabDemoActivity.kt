package com.ttv.demo

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin
import com.huobi.home.ui.StickyBottomTabPluginFactory

/**
 * 吸底Tab效果演示Activity
 * 展示如何使用重构后的StickyBottomTabPlugin
 */
class StickyBottomTabDemoActivity : AppCompatActivity() {

    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2
    
    private var stickyBottomTabPlugin: StickyBottomTabPlugin? = null
    
    // Tab数据
    private val tabTitles = listOf("推荐", "关注", "热门", "视频", "直播", "小说", "游戏", "科技")
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        setupTabs()
        setupViewPager()
        setupStickyBottomTabPlugin()
    }
    
    private fun initViews() {
        coordinatorLayout = findViewById(R.id.clLayout)
        appBarLayout = findViewById(R.id.appBarLayout)
        coIndicator = findViewById(R.id.coIndicator)
        viewPager = findViewById(R.id.home_viewPager)
    }
    
    private fun setupTabs() {
        // 使用IndicatorHelper设置CoIndicator
        IndicatorHelper.initBottomNoLineIndicator(
            this,
            tabTitles,
            coIndicator,
            0f,
            viewPager,
            16f,
            R.attr.Text_L1,
            R.attr.Text_L3,
            scaleSize = 20f,
            isBold = true,
            onTabClick = object : IndicatorHelper.TabClickListener {
                override fun onTabClick(index: Int) {
                    // 客户的业务逻辑：重复点击刷新
                    if (getCurrentTabPosition() == index) {
                        refreshContent(index)
                    }
                    debugLog("Tab点击-索引$index")
                }
            }
        )
        
        // 设置CoIndicator的PageSelectListener
        coIndicator.listener = object : CoIndicator.PageSelectListener {
            override fun onSelected(position: Int) {
                // 客户的埋点统计逻辑
                when (position) {
                    0 -> trackEvent("tab_recommend_selected")
                    1 -> trackEvent("tab_follow_selected")
                    2 -> trackEvent("tab_hot_selected")
                    3 -> trackEvent("tab_video_selected")
                    4 -> trackEvent("tab_live_selected")
                    5 -> trackEvent("tab_novel_selected")
                    6 -> trackEvent("tab_game_selected")
                    7 -> trackEvent("tab_tech_selected")
                }
                debugLog("PageSelectListener触发-位置$position")
            }
        }
    }
    
    private fun setupViewPager() {
        viewPager.adapter = TabPagerAdapter(this, tabTitles)
        viewPager.offscreenPageLimit = 3
    }
    
    private fun setupStickyBottomTabPlugin() {
        // 使用工厂方法创建插件，自动应用策略模式
        stickyBottomTabPlugin = StickyBottomTabPluginFactory.createForCoIndicator(
            context = this,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabTitles,
            preserveOriginalListeners = true  // 保留原始监听器
        )
        
        // 启用插件
        stickyBottomTabPlugin?.enable()
        
        debugLog("吸底Tab插件已启用")
    }
    
    private fun getCurrentTabPosition(): Int {
        return viewPager.currentItem
    }
    
    private fun refreshContent(position: Int) {
        // 模拟刷新内容
        debugLog("刷新内容-位置$position")
    }
    
    private fun trackEvent(eventName: String) {
        // 模拟埋点统计
        debugLog("埋点统计: $eventName")
    }
    
    private fun debugLog(message: String) {
        android.util.Log.d("StickyBottomTabDemo", message)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 销毁插件，释放资源
        stickyBottomTabPlugin?.destroy()
    }
    
    /**
     * ViewPager适配器
     */
    private class TabPagerAdapter(
        activity: FragmentActivity,
        private val tabTitles: List<String>
    ) : FragmentStateAdapter(activity) {
        
        override fun getItemCount(): Int = tabTitles.size
        
        override fun createFragment(position: Int): Fragment {
            return ContentFragment.newInstance(tabTitles[position], position)
        }
    }
}

/**
 * 模拟的IndicatorHelper类
 */
object IndicatorHelper {
    
    interface TabClickListener {
        fun onTabClick(index: Int)
    }
    
    fun initBottomNoLineIndicator(
        context: android.content.Context,
        tabs: List<String>,
        coIndicator: CoIndicator,
        indicatorHeight: Float,
        viewPager: ViewPager2,
        textSize: Float,
        normalTextColorAttr: Int,
        selectedTextColorAttr: Int,
        scaleSize: Float = 0f,
        isBold: Boolean = false,
        onTabClick: TabClickListener? = null
    ) {
        // 模拟IndicatorHelper的初始化逻辑
        tabs.forEachIndexed { index, title ->
            coIndicator.addTab(title)
        }
        
        // 设置点击监听器
        onTabClick?.let { listener ->
            val navigator = CommonNavigator(listener)
            // 通过反射设置navigator（模拟真实的IndicatorHelper行为）
            try {
                val navigatorField = coIndicator.javaClass.getDeclaredField("navigator")
                navigatorField.isAccessible = true
                navigatorField.set(coIndicator, navigator)
            } catch (e: Exception) {
                android.util.Log.w("IndicatorHelper", "设置navigator失败", e)
            }
        }
    }
}

/**
 * 模拟的CommonNavigator类
 */
class CommonNavigator(private val tabClickListener: IndicatorHelper.TabClickListener) {
    
    fun onTabClick(index: Int) {
        tabClickListener.onTabClick(index)
    }
}
